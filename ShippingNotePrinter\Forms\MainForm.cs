using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ShippingNotePrinter.Models;
using ShippingNotePrinter.Services;

namespace ShippingNotePrinter.Forms
{
    /// <summary>
    /// 主窗体 - 发运单打印系统
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly SoapService _soapService;
        private readonly SmsService _smsService;
        private readonly PrintService _printService;
        private List<ShippingNote> _shippingNotes;
        private int _currentStep;
        private string _currentPhoneNumber;

        public MainForm()
        {
            InitializeComponent();
            
            // 初始化服务
            _soapService = new SoapService();
            _smsService = new SmsService();
            _printService = new PrintService();
            
            // 初始化界面
            InitializeUI();
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitializeUI()
        {
            _currentStep = 0; // 0表示主界面
            _shippingNotes = new List<ShippingNote>();

            // 显示主界面
            ShowMainInterface();

            // 设置触控友好的界面
            SetupTouchFriendlyUI();

            // 启动时间更新定时器
            StartTimeUpdater();
        }

        /// <summary>
        /// 显示主界面（卡片式布局）
        /// </summary>
        private void ShowMainInterface()
        {
            // 隐藏所有步骤面板
            panelStep1.Visible = false;
            panelStep2.Visible = false;
            panelStep3.Visible = false;

            // 显示卡片容器（如果存在）
            if (panelCardContainer != null)
            {
                panelCardContainer.Visible = true;
                // 重置卡片状态
                ResetCardStates();
            }
            else
            {
                // 如果卡片容器不存在，显示第一步
                panelStep1.Visible = true;
            }

            UpdateStatus("请选择需要的功能");
        }

        /// <summary>
        /// 重置卡片状态
        /// </summary>
        private void ResetCardStates()
        {
            // 如果卡片控件存在，则重置状态
            if (cardPhoneInput != null)
            {
                cardPhoneInput.Enabled = true;
                SetCardOpacity(cardPhoneInput, 1.0f);
            }

            if (cardVerifyCode != null)
            {
                cardVerifyCode.Enabled = false;
                SetCardOpacity(cardVerifyCode, 0.5f);
            }

            if (cardSelectNotes != null)
            {
                cardSelectNotes.Enabled = false;
                SetCardOpacity(cardSelectNotes, 0.5f);
            }

            if (cardPrintPreview != null)
            {
                cardPrintPreview.Enabled = false;
                SetCardOpacity(cardPrintPreview, 0.5f);
            }

            if (cardPrintNotes != null)
            {
                cardPrintNotes.Enabled = false;
                SetCardOpacity(cardPrintNotes, 0.5f);
            }

            if (cardHelp != null)
            {
                cardHelp.Enabled = true;
                SetCardOpacity(cardHelp, 1.0f);
            }
        }

        /// <summary>
        /// 设置卡片透明度
        /// </summary>
        private void SetCardOpacity(Panel card, float opacity)
        {
            // 通过调整背景色的Alpha值来模拟透明度效果
            var originalColor = card.BackColor;
            int alpha = (int)(255 * opacity);
            card.BackColor = Color.FromArgb(alpha, originalColor.R, originalColor.G, originalColor.B);
        }

        /// <summary>
        /// 启动时间更新器
        /// </summary>
        private void StartTimeUpdater()
        {
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 1000; // 每秒更新
            timer.Tick += (s, e) => {
                lblTime.Text = DateTime.Now.ToString("HH:mm:ss");
            };
            timer.Start();
        }

        /// <summary>
        /// 设置触控友好的界面
        /// </summary>
        private void SetupTouchFriendlyUI()
        {
            // 设置窗体全屏显示（可选）
            // this.WindowState = FormWindowState.Maximized;

            // 设置触控友好的控件样式
            foreach (Control control in GetAllControls(this))
            {
                if (control is Button button)
                {
                    // Designer中已设置了触控友好的按钮样式
                    // 这里只需要添加悬停效果
                    SetupButtonHoverEffects(button);
                }
                else if (control is ListView listView)
                {
                    // 优化列表视图的触控体验
                    listView.Font = new System.Drawing.Font("微软雅黑", 14F);
                    listView.View = View.Details;
                    listView.FullRowSelect = true;
                    listView.GridLines = true;
                    listView.CheckBoxes = true;
                    listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;

                    // 设置更大的行高以便触控
                    listView.OwnerDraw = true;
                    listView.DrawItem += ListView_DrawItem;
                    listView.DrawSubItem += ListView_DrawSubItem;
                    listView.DrawColumnHeader += ListView_DrawColumnHeader;
                }
                else if (control is Label label && label.Name.StartsWith("lblStep"))
                {
                    // 步骤标签样式优化
                    label.BackColor = System.Drawing.Color.FromArgb(248, 249, 250);
                    label.ForeColor = System.Drawing.Color.FromArgb(52, 144, 220);
                }
                else if (control is Label label2 && (label2.Name.Contains("PhoneNumber") || label2.Name.Contains("VerificationCode")))
                {
                    // 输入提示标签优化
                    label2.Font = new System.Drawing.Font("微软雅黑", 16F, System.Drawing.FontStyle.Bold);
                    label2.ForeColor = System.Drawing.Color.FromArgb(73, 80, 87);
                }
            }

            // 添加面板圆角效果
            AddPanelShadowEffect();
        }

        /// <summary>
        /// 设置按钮悬停效果
        /// </summary>
        private void SetupButtonHoverEffects(Button button)
        {
            var originalColor = button.BackColor;

            button.MouseEnter += (s, e) => {
                button.BackColor = AdjustBrightness(originalColor, 0.15f);
                button.Cursor = Cursors.Hand;
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = originalColor;
                button.Cursor = Cursors.Default;
            };
        }

        /// <summary>
        /// 添加面板圆角效果
        /// </summary>
        private void AddPanelShadowEffect()
        {
            // 为步骤面板添加圆角效果
            panelStep1.Paint += Panel_Paint;
            panelStep2.Paint += Panel_Paint;
            panelStep3.Paint += Panel_Paint;
        }

        /// <summary>
        /// 面板绘制事件 - 添加圆角效果
        /// </summary>
        private void Panel_Paint(object sender, PaintEventArgs e)
        {
            Panel panel = sender as Panel;
            if (panel != null)
            {
                // 绘制圆角矩形背景
                using (var path = GetRoundedRectanglePath(panel.ClientRectangle, 8))
                {
                    panel.Region = new System.Drawing.Region(path);
                }
            }
        }

        /// <summary>
        /// 获取圆角矩形路径
        /// </summary>
        private System.Drawing.Drawing2D.GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        /// <summary>
        /// ListView自定义绘制 - 增加行高
        /// </summary>
        private void ListView_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// ListView自定义绘制子项
        /// </summary>
        private void ListView_DrawSubItem(object sender, DrawListViewSubItemEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// ListView自定义绘制列头
        /// </summary>
        private void ListView_DrawColumnHeader(object sender, DrawListViewColumnHeaderEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// 设置按钮主题颜色
        /// </summary>
        private void SetButtonTheme(Button button)
        {
            // 主要操作按钮 - 蓝色主题
            if (button.Name.Contains("Send") || button.Name.Contains("Verify") || button.Name.Contains("Print"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(52, 144, 220);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 次要操作按钮 - 绿色主题
            else if (button.Name.Contains("Preview") || button.Name.Contains("Select"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(40, 167, 69);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 返回/取消按钮 - 灰色主题
            else if (button.Name.Contains("Back") || button.Name.Contains("Cancel"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(108, 117, 125);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 默认按钮样式
            else
            {
                button.BackColor = System.Drawing.Color.FromArgb(248, 249, 250);
                button.ForeColor = System.Drawing.Color.FromArgb(33, 37, 41);
                button.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(206, 212, 218);
                button.FlatAppearance.BorderSize = 1;
            }

            // 添加悬停效果
            button.FlatAppearance.MouseOverBackColor = AdjustBrightness(button.BackColor, 0.1f);
        }

        /// <summary>
        /// 调整颜色亮度
        /// </summary>
        private System.Drawing.Color AdjustBrightness(System.Drawing.Color color, float factor)
        {
            int r = Math.Min(255, Math.Max(0, (int)(color.R * (1 + factor))));
            int g = Math.Min(255, Math.Max(0, (int)(color.G * (1 + factor))));
            int b = Math.Min(255, Math.Max(0, (int)(color.B * (1 + factor))));
            return System.Drawing.Color.FromArgb(r, g, b);
        }

        /// <summary>
        /// 获取所有子控件
        /// </summary>
        private IEnumerable<Control> GetAllControls(Control container)
        {
            var controls = container.Controls.Cast<Control>();
            return controls.SelectMany(ctrl => GetAllControls(ctrl)).Concat(controls);
        }

        #region 卡片点击事件处理

        /// <summary>
        /// 手机号验证卡片点击事件
        /// </summary>
        private void cardPhoneInput_Click(object sender, EventArgs e)
        {
            if (cardPhoneInput != null && !cardPhoneInput.Enabled) return;

            // 显示第一步面板
            ShowStep(1);
        }

        /// <summary>
        /// 验证码确认卡片点击事件
        /// </summary>
        private void cardVerifyCode_Click(object sender, EventArgs e)
        {
            if (cardVerifyCode != null && !cardVerifyCode.Enabled) return;

            ShowStep(2);
        }

        /// <summary>
        /// 选择发运单卡片点击事件
        /// </summary>
        private void cardSelectNotes_Click(object sender, EventArgs e)
        {
            if (cardSelectNotes != null && !cardSelectNotes.Enabled) return;

            ShowStep(3);
        }

        /// <summary>
        /// 打印预览卡片点击事件
        /// </summary>
        private void cardPrintPreview_Click(object sender, EventArgs e)
        {
            if (cardPrintPreview != null && !cardPrintPreview.Enabled) return;

            btnPreview_Click(sender, e);
        }

        /// <summary>
        /// 打印发运单卡片点击事件
        /// </summary>
        private void cardPrintNotes_Click(object sender, EventArgs e)
        {
            if (cardPrintNotes != null && !cardPrintNotes.Enabled) return;

            btnPrint_Click(sender, e);
        }

        /// <summary>
        /// 帮助卡片点击事件
        /// </summary>
        private void cardHelp_Click(object sender, EventArgs e)
        {
            ShowHelpDialog();
        }

        #endregion

        #region 面板显示方法

        /// <summary>
        /// 返回主界面按钮点击事件
        /// </summary>
        private void btnBackToMain_Click(object sender, EventArgs e)
        {
            ShowMainInterface();
        }

        /// <summary>
        /// 显示帮助对话框
        /// </summary>
        private void ShowHelpDialog()
        {
            string helpText = @"发运单打印系统使用说明：

1. 📱 手机号验证
   - 输入您的手机号码
   - 点击发送验证码

2. 🔐 验证码确认
   - 输入收到的短信验证码
   - 点击验证并继续

3. 📋 选择发运单
   - 从列表中选择要打印的发运单
   - 可以多选

4. 👁️ 打印预览
   - 预览发运单内容

5. 🖨️ 打印发运单
   - 确认打印选中的发运单

如有问题，请联系管理员。";

            MessageBox.Show(helpText, "操作帮助", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        /// <summary>
        /// 显示指定步骤
        /// </summary>
        private void ShowStep(int step)
        {
            _currentStep = step;

            // 隐藏所有步骤面板
            panelStep1.Visible = false;
            panelStep2.Visible = false;
            panelStep3.Visible = false;

            // 隐藏卡片容器（如果存在）
            if (panelCardContainer != null)
                panelCardContainer.Visible = false;

            // 显示当前步骤
            switch (step)
            {
                case 1:
                    panelStep1.Visible = true;
                    btnBack.Visible = false;
                    txtPhoneNumber.Focus();
                    break;
                case 2:
                    panelStep2.Visible = true;
                    btnBack.Visible = true;
                    txtVerificationCode.Focus();
                    break;
                case 3:
                    panelStep3.Visible = true;
                    btnBack.Visible = true;
                    break;
            }

            UpdateStatus("");
        }

        /// <summary>
        /// 发送验证码按钮点击事件
        /// </summary>
        private async void btnSendCode_Click(object sender, EventArgs e)
        {
            try
            {
                string phoneNumber = txtPhoneNumber.Text.Trim();
                
                // 验证手机号格式
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    MessageBox.Show("请输入正确的手机号码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPhoneNumber.Focus();
                    return;
                }
                
                _currentPhoneNumber = phoneNumber;
                
                // 禁用按钮，显示加载状态
                btnSendCode.Enabled = false;
                btnSendCode.Text = "发送中...";
                UpdateStatus("正在发送验证码...");
                
                // 发送验证码
                bool success = await Task.Run(() => _smsService.SendVerificationCode(phoneNumber));
                
                if (success)
                {
                    MessageBox.Show("验证码已发送到您的手机，请查收！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 启用验证码卡片（如果存在）
                    if (cardVerifyCode != null)
                    {
                        cardVerifyCode.Enabled = true;
                        SetCardOpacity(cardVerifyCode, 1.0f);
                    }

                    // 显示第二步
                    ShowStep(2);
                }
                else
                {
                    MessageBox.Show("验证码发送失败，请重试！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发送验证码时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSendCode.Enabled = true;
                btnSendCode.Text = "发送验证码";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 验证验证码按钮点击事件
        /// </summary>
        private async void btnVerifyCode_Click(object sender, EventArgs e)
        {
            try
            {
                string verificationCode = txtVerificationCode.Text.Trim();
                
                if (string.IsNullOrEmpty(verificationCode))
                {
                    MessageBox.Show("请输入验证码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtVerificationCode.Focus();
                    return;
                }
                
                // 禁用按钮，显示加载状态
                btnVerifyCode.Enabled = false;
                btnVerifyCode.Text = "验证中...";
                UpdateStatus("正在验证验证码...");
                
                // 验证验证码
                bool success = await Task.Run(() => _smsService.VerifyCode(_currentPhoneNumber, verificationCode));
                
                if (success)
                {
                    // 验证成功，启用后续卡片（如果存在）
                    if (cardSelectNotes != null)
                    {
                        cardSelectNotes.Enabled = true;
                        SetCardOpacity(cardSelectNotes, 1.0f);
                    }
                    if (cardPrintPreview != null)
                    {
                        cardPrintPreview.Enabled = true;
                        SetCardOpacity(cardPrintPreview, 1.0f);
                    }
                    if (cardPrintNotes != null)
                    {
                        cardPrintNotes.Enabled = true;
                        SetCardOpacity(cardPrintNotes, 1.0f);
                    }

                    // 查询发运单
                    await LoadShippingNotes();
                }
                else
                {
                    MessageBox.Show("验证码错误，请重新输入！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtVerificationCode.SelectAll();
                    txtVerificationCode.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证验证码时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnVerifyCode.Enabled = true;
                btnVerifyCode.Text = "验证";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 加载发运单列表
        /// </summary>
        private async Task LoadShippingNotes()
        {
            try
            {
                UpdateStatus("正在查询发运单...");
                
                // 查询发运单
                _shippingNotes = await Task.Run(() => _soapService.GetShippingNotesByPhone(_currentPhoneNumber));
                
                if (_shippingNotes == null || _shippingNotes.Count == 0)
                {
                    MessageBox.Show("未找到相关的发运单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ShowMainInterface();
                    return;
                }

                // 填充发运单列表
                PopulateShippingNotesList();

                // 显示第三步
                ShowStep(3);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询发运单时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ShowStep(1);
            }
        }

        /// <summary>
        /// 验证手机号格式
        /// </summary>
        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber) || phoneNumber.Length != 11)
                return false;
                
            return phoneNumber.All(char.IsDigit) && phoneNumber.StartsWith("1");
        }

        /// <summary>
        /// 填充发运单列表
        /// </summary>
        private void PopulateShippingNotesList()
        {
            listViewShippingNotes.Items.Clear();

            foreach (var note in _shippingNotes)
            {
                ListViewItem item = new ListViewItem(note.ShippingNoteNumber);
                item.SubItems.Add(note.CustomerUnit);
                item.SubItems.Add(note.ShippingWarehouse);
                item.SubItems.Add(note.DeliveryMethod);
                item.SubItems.Add(note.ShippingDate.ToString("yyyy-MM-dd"));
                item.Tag = note;

                listViewShippingNotes.Items.Add(item);
            }

            lblShippingNotesCount.Text = $"共找到 {_shippingNotes.Count} 张发运单";
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in listViewShippingNotes.Items)
            {
                item.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选按钮点击事件
        /// </summary>
        private void btnSelectNone_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in listViewShippingNotes.Items)
            {
                item.Checked = false;
            }
        }

        /// <summary>
        /// 打印预览按钮点击事件
        /// </summary>
        private async void btnPreview_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取选中的发运单
                var selectedNotes = GetSelectedShippingNotes();

                if (selectedNotes.Count == 0)
                {
                    MessageBox.Show("请至少选择一张发运单进行预览！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 预览第一张选中的发运单
                var note = selectedNotes[0];

                // 禁用按钮，显示加载状态
                btnPreview.Enabled = false;
                btnPreview.Text = "加载中...";
                UpdateStatus("正在加载发运单详情...");

                // 获取发运单详细信息
                var detailNote = await Task.Run(() => _soapService.GetShippingNoteDetail(note.ShippingNoteNumber));

                // 显示打印预览
                _printService.ShowPrintPreview(detailNote);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示打印预览时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnPreview.Enabled = true;
                btnPreview.Text = "打印预览";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private async void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取选中的发运单
                var selectedNotes = GetSelectedShippingNotes();

                if (selectedNotes.Count == 0)
                {
                    MessageBox.Show("请至少选择一张发运单进行打印！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 确认打印
                string message = $"确定要打印选中的 {selectedNotes.Count} 张发运单吗？";
                if (MessageBox.Show(message, "确认打印", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                {
                    return;
                }

                // 禁用按钮，显示加载状态
                btnPrint.Enabled = false;
                btnPrint.Text = "打印中...";
                UpdateStatus("正在打印发运单...");

                // 逐个打印发运单
                int successCount = 0;
                foreach (var note in selectedNotes)
                {
                    try
                    {
                        // 获取发运单详细信息
                        var detailNote = await Task.Run(() => _soapService.GetShippingNoteDetail(note.ShippingNoteNumber));

                        // 打印发运单
                        await Task.Run(() => _printService.PrintShippingNoteDirect(detailNote));

                        successCount++;
                        UpdateStatus($"已打印 {successCount}/{selectedNotes.Count} 张发运单...");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"打印发运单 {note.ShippingNoteNumber} 时发生错误：{ex.Message}",
                            "打印错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }

                MessageBox.Show($"打印完成！成功打印 {successCount} 张发运单。", "打印完成",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // 打印完成后返回主界面
                ShowMainInterface();
                ResetForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印过程中发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnPrint.Enabled = true;
                btnPrint.Text = "打印发运单";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 获取选中的发运单
        /// </summary>
        private List<ShippingNote> GetSelectedShippingNotes()
        {
            var selectedNotes = new List<ShippingNote>();

            foreach (ListViewItem item in listViewShippingNotes.Items)
            {
                if (item.Checked && item.Tag is ShippingNote note)
                {
                    selectedNotes.Add(note);
                }
            }

            return selectedNotes;
        }

        /// <summary>
        /// 返回按钮点击事件
        /// </summary>
        private void btnBack_Click(object sender, EventArgs e)
        {
            if (_currentStep > 1)
            {
                ShowStep(_currentStep - 1);
            }
        }

        /// <summary>
        /// 重置表单
        /// </summary>
        private void ResetForm()
        {
            txtPhoneNumber.Text = "";
            txtVerificationCode.Text = "";
            _currentPhoneNumber = "";
            _shippingNotes.Clear();
            listViewShippingNotes.Items.Clear();
            lblShippingNotesCount.Text = "共找到 0 张发运单";
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatus(string message)
        {
            lblStatus.Text = message;
            Application.DoEvents();
        }

        /// <summary>
        /// 窗体关闭时释放资源
        /// </summary>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _printService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
