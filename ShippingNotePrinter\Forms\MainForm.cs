using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ShippingNotePrinter.Models;
using ShippingNotePrinter.Services;
using ShippingNotePrinter.Styles;

namespace ShippingNotePrinter.Forms
{
    /// <summary>
    /// 主窗体 - 发运单打印系统
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly SoapService _soapService;
        private readonly SmsService _smsService;
        private readonly PrintService _printService;
        private List<ShippingNote> _shippingNotes;
        private int _currentStep;
        private string _currentPhoneNumber;

        public MainForm()
        {
            InitializeComponent();

            // 初始化服务
            _soapService = new SoapService();
            _smsService = new SmsService();
            _printService = new PrintService();

            // 初始化界面
            InitializeUI();

            // 添加窗体大小变化事件处理
            this.Resize += MainForm_Resize;
            this.Load += MainForm_Load;
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitializeUI()
        {
            _currentStep = 0; // 0表示主界面
            _shippingNotes = new List<ShippingNote>();

            // 应用现代化样式
            ApplyModernStyles();

            // 显示主界面
            ShowMainInterface();

            // 设置触控友好的界面
            SetupTouchFriendlyUI();

            // 启动时间更新定时器
            StartTimeUpdater();
        }

        /// <summary>
        /// 显示主界面（只显示手机输入选项）
        /// </summary>
        private void ShowMainInterface()
        {
            // 隐藏所有步骤面板
            if (panelStep1 != null) panelStep1.Visible = false;
            if (panelStep2 != null) panelStep2.Visible = false;
            if (panelStep3 != null) panelStep3.Visible = false;
            if (panelPhoneInput != null) panelPhoneInput.Visible = false;
            if (panelVerification != null) panelVerification.Visible = false;
            if (panelNotesList != null) panelNotesList.Visible = false;

            // 显示主界面卡片容器
            if (panelCardContainer != null)
            {
                panelCardContainer.Visible = true;
                // 只显示手机输入卡片，隐藏其他卡片
                ShowOnlyPhoneInputCard();
            }

            UpdateStatus("请输入手机号码开始验证");
        }

        /// <summary>
        /// 只显示手机输入卡片
        /// </summary>
        private void ShowOnlyPhoneInputCard()
        {
            // 只显示手机输入卡片，隐藏其他所有卡片
            if (cardPhoneInput != null)
            {
                cardPhoneInput.Visible = true;
                cardPhoneInput.Enabled = true;
            }

            if (cardVerifyCode != null) cardVerifyCode.Visible = false;
            if (cardSelectNotes != null) cardSelectNotes.Visible = false;
            if (cardPrintPreview != null) cardPrintPreview.Visible = false;
            if (cardPrintNotes != null) cardPrintNotes.Visible = false;
            if (cardHelp != null) cardHelp.Visible = false;
        }

        /// <summary>
        /// 重置卡片状态
        /// </summary>
        private void ResetCardStates()
        {
            // 重置所有卡片为可见状态（用于其他需要的场景）
            if (cardPhoneInput != null)
            {
                cardPhoneInput.Visible = true;
                cardPhoneInput.Enabled = true;
            }

            if (cardVerifyCode != null)
            {
                cardVerifyCode.Visible = true;
                cardVerifyCode.Enabled = false;
            }

            if (cardSelectNotes != null)
            {
                cardSelectNotes.Visible = true;
                cardSelectNotes.Enabled = false;
            }

            if (cardPrintPreview != null)
            {
                cardPrintPreview.Visible = true;
                cardPrintPreview.Enabled = false;
            }

            if (cardPrintNotes != null)
            {
                cardPrintNotes.Visible = true;
                cardPrintNotes.Enabled = false;
            }

            if (cardHelp != null)
            {
                cardHelp.Visible = true;
                cardHelp.Enabled = true;
            }
        }

        /// <summary>
        /// 设置卡片透明度
        /// </summary>
        private void SetCardOpacity(Panel card, float opacity)
        {
            // 通过调整背景色的Alpha值来模拟透明度效果
            var originalColor = card.BackColor;
            int alpha = (int)(255 * opacity);
            card.BackColor = Color.FromArgb(alpha, originalColor.R, originalColor.G, originalColor.B);
        }

        /// <summary>
        /// 启动时间更新器
        /// </summary>
        private void StartTimeUpdater()
        {
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 1000; // 每秒更新
            timer.Tick += (s, e) => {
                lblTime.Text = DateTime.Now.ToString("HH:mm:ss");
            };
            timer.Start();
        }

        /// <summary>
        /// 设置触控友好的界面
        /// </summary>
        private void SetupTouchFriendlyUI()
        {
            // 设置窗体全屏显示（可选）
            // this.WindowState = FormWindowState.Maximized;

            // 设置触控友好的控件样式
            foreach (Control control in GetAllControls(this))
            {
                if (control is Button button)
                {
                    // Designer中已设置了触控友好的按钮样式
                    // 这里只需要添加悬停效果
                    SetupButtonHoverEffects(button);
                }
                else if (control is ListView listView)
                {
                    // 优化列表视图的触控体验
                    listView.Font = new System.Drawing.Font("微软雅黑", 14F);
                    listView.View = View.Details;
                    listView.FullRowSelect = true;
                    listView.GridLines = true;
                    listView.CheckBoxes = true;
                    listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;

                    // 设置更大的行高以便触控
                    listView.OwnerDraw = true;
                    listView.DrawItem += ListView_DrawItem;
                    listView.DrawSubItem += ListView_DrawSubItem;
                    listView.DrawColumnHeader += ListView_DrawColumnHeader;
                }
                else if (control is Label label && label.Name.StartsWith("lblStep"))
                {
                    // 步骤标签样式优化
                    label.BackColor = System.Drawing.Color.FromArgb(248, 249, 250);
                    label.ForeColor = System.Drawing.Color.FromArgb(52, 144, 220);
                }
                else if (control is Label label2 && (label2.Name.Contains("PhoneNumber") || label2.Name.Contains("VerificationCode")))
                {
                    // 输入提示标签优化
                    label2.Font = new System.Drawing.Font("微软雅黑", 16F, System.Drawing.FontStyle.Bold);
                    label2.ForeColor = System.Drawing.Color.FromArgb(73, 80, 87);
                }
            }

            // 添加面板圆角效果
            AddPanelShadowEffect();
        }

        /// <summary>
        /// 设置按钮悬停效果
        /// </summary>
        private void SetupButtonHoverEffects(Button button)
        {
            var originalColor = button.BackColor;

            button.MouseEnter += (s, e) => {
                button.BackColor = AdjustBrightness(originalColor, 0.15f);
                button.Cursor = Cursors.Hand;
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = originalColor;
                button.Cursor = Cursors.Default;
            };
        }

        /// <summary>
        /// 添加面板圆角效果
        /// </summary>
        private void AddPanelShadowEffect()
        {
            // 为步骤面板添加圆角效果
            panelStep1.Paint += Panel_Paint;
            panelStep2.Paint += Panel_Paint;
            panelStep3.Paint += Panel_Paint;
        }

        /// <summary>
        /// 面板绘制事件 - 添加圆角效果
        /// </summary>
        private void Panel_Paint(object sender, PaintEventArgs e)
        {
            Panel panel = sender as Panel;
            if (panel != null)
            {
                // 绘制圆角矩形背景
                using (var path = GetRoundedRectanglePath(panel.ClientRectangle, 8))
                {
                    panel.Region = new System.Drawing.Region(path);
                }
            }
        }

        /// <summary>
        /// 获取圆角矩形路径
        /// </summary>
        private System.Drawing.Drawing2D.GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        /// <summary>
        /// ListView自定义绘制 - 增加行高
        /// </summary>
        private void ListView_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// ListView自定义绘制子项
        /// </summary>
        private void ListView_DrawSubItem(object sender, DrawListViewSubItemEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// ListView自定义绘制列头
        /// </summary>
        private void ListView_DrawColumnHeader(object sender, DrawListViewColumnHeaderEventArgs e)
        {
            e.DrawDefault = true;
        }

        /// <summary>
        /// 设置按钮主题颜色
        /// </summary>
        private void SetButtonTheme(Button button)
        {
            // 主要操作按钮 - 蓝色主题
            if (button.Name.Contains("Send") || button.Name.Contains("Verify") || button.Name.Contains("Print"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(52, 144, 220);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 次要操作按钮 - 绿色主题
            else if (button.Name.Contains("Preview") || button.Name.Contains("Select"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(40, 167, 69);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 返回/取消按钮 - 灰色主题
            else if (button.Name.Contains("Back") || button.Name.Contains("Cancel"))
            {
                button.BackColor = System.Drawing.Color.FromArgb(108, 117, 125);
                button.ForeColor = System.Drawing.Color.White;
            }
            // 默认按钮样式
            else
            {
                button.BackColor = System.Drawing.Color.FromArgb(248, 249, 250);
                button.ForeColor = System.Drawing.Color.FromArgb(33, 37, 41);
                button.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(206, 212, 218);
                button.FlatAppearance.BorderSize = 1;
            }

            // 添加悬停效果
            button.FlatAppearance.MouseOverBackColor = AdjustBrightness(button.BackColor, 0.1f);
        }

        /// <summary>
        /// 调整颜色亮度
        /// </summary>
        private System.Drawing.Color AdjustBrightness(System.Drawing.Color color, float factor)
        {
            int r = Math.Min(255, Math.Max(0, (int)(color.R * (1 + factor))));
            int g = Math.Min(255, Math.Max(0, (int)(color.G * (1 + factor))));
            int b = Math.Min(255, Math.Max(0, (int)(color.B * (1 + factor))));
            return System.Drawing.Color.FromArgb(r, g, b);
        }

        /// <summary>
        /// 获取所有子控件
        /// </summary>
        private IEnumerable<Control> GetAllControls(Control container)
        {
            var controls = container.Controls.Cast<Control>();
            return controls.SelectMany(ctrl => GetAllControls(ctrl)).Concat(controls);
        }

        #region 卡片点击事件处理

        /// <summary>
        /// 手机号验证卡片点击事件
        /// </summary>
        private void cardPhoneInput_Click(object sender, EventArgs e)
        {
            if (cardPhoneInput != null && !cardPhoneInput.Enabled) return;

            // 显示第一步面板
            ShowStep(1);
        }

        /// <summary>
        /// 验证码确认卡片点击事件
        /// </summary>
        private void cardVerifyCode_Click(object sender, EventArgs e)
        {
            if (cardVerifyCode != null && !cardVerifyCode.Enabled) return;

            ShowStep(2);
        }

        /// <summary>
        /// 选择发运单卡片点击事件
        /// </summary>
        private void cardSelectNotes_Click(object sender, EventArgs e)
        {
            if (cardSelectNotes != null && !cardSelectNotes.Enabled) return;

            ShowStep(3);
        }

        /// <summary>
        /// 打印预览卡片点击事件
        /// </summary>
        private void cardPrintPreview_Click(object sender, EventArgs e)
        {
            if (cardPrintPreview != null && !cardPrintPreview.Enabled) return;

            btnPreview_Click(sender, e);
        }

        /// <summary>
        /// 打印发运单卡片点击事件
        /// </summary>
        private void cardPrintNotes_Click(object sender, EventArgs e)
        {
            if (cardPrintNotes != null && !cardPrintNotes.Enabled) return;

            btnPrint_Click(sender, e);
        }

        /// <summary>
        /// 帮助卡片点击事件
        /// </summary>
        private void cardHelp_Click(object sender, EventArgs e)
        {
            ShowHelpDialog();
        }

        #endregion

        #region 面板显示方法

        /// <summary>
        /// 返回主界面按钮点击事件
        /// </summary>
        private void btnBackToMain_Click(object sender, EventArgs e)
        {
            ShowMainInterface();
        }

        /// <summary>
        /// 显示帮助对话框
        /// </summary>
        private void ShowHelpDialog()
        {
            string helpText = @"发运单打印系统使用说明：

1. 📱 手机号验证
   - 输入您的手机号码
   - 点击发送验证码

2. 🔐 验证码确认
   - 输入收到的短信验证码
   - 点击验证并继续

3. 📋 选择发运单
   - 从列表中选择要打印的发运单
   - 可以多选

4. 👁️ 打印预览
   - 预览发运单内容

5. 🖨️ 打印发运单
   - 确认打印选中的发运单

如有问题，请联系管理员。";

            MessageBox.Show(helpText, "操作帮助", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        /// <summary>
        /// 显示指定步骤
        /// </summary>
        private void ShowStep(int step)
        {
            _currentStep = step;

            // 隐藏所有面板
            if (panelStep1 != null) panelStep1.Visible = false;
            if (panelStep2 != null) panelStep2.Visible = false;
            if (panelStep3 != null) panelStep3.Visible = false;
            if (panelPhoneInput != null) panelPhoneInput.Visible = false;
            if (panelVerification != null) panelVerification.Visible = false;
            if (panelNotesList != null) panelNotesList.Visible = false;
            if (panelCardContainer != null) panelCardContainer.Visible = false;

            // 显示当前步骤
            switch (step)
            {
                case 1:
                    if (panelStep1 != null)
                    {
                        panelStep1.Visible = true;
                        AdjustStepPanelLayout(panelStep1);
                        if (txtPhoneNumber != null) txtPhoneNumber.Focus();
                    }
                    UpdateStatus("请输入手机号码");
                    break;
                case 2:
                    if (panelStep2 != null)
                    {
                        panelStep2.Visible = true;
                        AdjustStepPanelLayout(panelStep2);
                        if (txtVerificationCode != null) txtVerificationCode.Focus();
                    }
                    UpdateStatus("请输入验证码");
                    break;
                case 3:
                    if (panelStep3 != null)
                    {
                        panelStep3.Visible = true;
                        AdjustStepPanelLayout(panelStep3);
                    }
                    UpdateStatus("请选择要打印的发运单");
                    break;
            }
        }

        /// <summary>
        /// 发送验证码按钮点击事件
        /// </summary>
        private async void btnSendCode_Click(object sender, EventArgs e)
        {
            try
            {
                string phoneNumber = txtPhoneNumber.Text.Trim();
                
                // 验证手机号格式
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    MessageBox.Show("请输入正确的手机号码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPhoneNumber.Focus();
                    return;
                }
                
                _currentPhoneNumber = phoneNumber;
                
                // 禁用按钮，显示加载状态
                btnSendCode.Enabled = false;
                btnSendCode.Text = "发送中...";
                UpdateStatus("正在发送验证码...");
                
                // 发送验证码
                bool success = await Task.Run(() => _smsService.SendVerificationCode(phoneNumber));
                
                if (success)
                {
                    MessageBox.Show("验证码已发送到您的手机，请查收！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 直接显示第二步
                    ShowStep(2);
                }
                else
                {
                    MessageBox.Show("验证码发送失败，请重试！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发送验证码时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSendCode.Enabled = true;
                btnSendCode.Text = "发送验证码";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 验证验证码按钮点击事件
        /// </summary>
        private async void btnVerifyCode_Click(object sender, EventArgs e)
        {
            try
            {
                string verificationCode = txtVerificationCode.Text.Trim();
                
                if (string.IsNullOrEmpty(verificationCode))
                {
                    MessageBox.Show("请输入验证码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtVerificationCode.Focus();
                    return;
                }
                
                // 禁用按钮，显示加载状态
                btnVerifyCode.Enabled = false;
                btnVerifyCode.Text = "验证中...";
                UpdateStatus("正在验证验证码...");
                
                // 验证验证码
                bool success = await Task.Run(() => _smsService.VerifyCode(_currentPhoneNumber, verificationCode));
                
                if (success)
                {
                    // 验证成功，直接查询发运单并跳转到步骤3
                    await LoadShippingNotes();
                }
                else
                {
                    MessageBox.Show("验证码错误，请重新输入！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtVerificationCode.SelectAll();
                    txtVerificationCode.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证验证码时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnVerifyCode.Enabled = true;
                btnVerifyCode.Text = "验证";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 加载发运单列表
        /// </summary>
        private async Task LoadShippingNotes()
        {
            try
            {
                UpdateStatus("正在查询发运单...");
                
                // 查询发运单
                _shippingNotes = await Task.Run(() => _soapService.GetShippingNotesByPhone(_currentPhoneNumber));
                
                if (_shippingNotes == null || _shippingNotes.Count == 0)
                {
                    MessageBox.Show("未找到相关的发运单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ShowMainInterface();
                    return;
                }

                // 填充发运单列表
                PopulateShippingNotesList();

                // 显示第三步
                ShowStep(3);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询发运单时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ShowStep(1);
            }
        }

        /// <summary>
        /// 验证手机号格式
        /// </summary>
        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber) || phoneNumber.Length != 11)
                return false;
                
            return phoneNumber.All(char.IsDigit) && phoneNumber.StartsWith("1");
        }

        /// <summary>
        /// 填充发运单列表
        /// </summary>
        private void PopulateShippingNotesList()
        {
            listViewShippingNotes.Items.Clear();

            foreach (var note in _shippingNotes)
            {
                ListViewItem item = new ListViewItem(note.ShippingNoteNumber);
                item.SubItems.Add(note.CustomerUnit);
                item.SubItems.Add(note.ShippingWarehouse);
                item.SubItems.Add(note.DeliveryMethod);
                item.SubItems.Add(note.ShippingDate.ToString("yyyy-MM-dd"));
                item.Tag = note;

                listViewShippingNotes.Items.Add(item);
            }

            lblShippingNotesCount.Text = $"共找到 {_shippingNotes.Count} 张发运单";
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in listViewShippingNotes.Items)
            {
                item.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选按钮点击事件
        /// </summary>
        private void btnSelectNone_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in listViewShippingNotes.Items)
            {
                item.Checked = false;
            }
        }

        /// <summary>
        /// 打印预览按钮点击事件
        /// </summary>
        private async void btnPreview_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取选中的发运单
                var selectedNotes = GetSelectedShippingNotes();

                if (selectedNotes.Count == 0)
                {
                    MessageBox.Show("请至少选择一张发运单进行预览！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 预览第一张选中的发运单
                var note = selectedNotes[0];

                // 禁用按钮，显示加载状态
                btnPreview.Enabled = false;
                btnPreview.Text = "加载中...";
                UpdateStatus("正在加载发运单详情...");

                // 获取发运单详细信息
                var detailNote = await Task.Run(() => _soapService.GetShippingNoteDetail(note.ShippingNoteNumber));

                // 显示打印预览
                _printService.ShowPrintPreview(detailNote);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示打印预览时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnPreview.Enabled = true;
                btnPreview.Text = "打印预览";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private async void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取选中的发运单
                var selectedNotes = GetSelectedShippingNotes();

                if (selectedNotes.Count == 0)
                {
                    MessageBox.Show("请至少选择一张发运单进行打印！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 确认打印
                string message = $"确定要打印选中的 {selectedNotes.Count} 张发运单吗？";
                if (MessageBox.Show(message, "确认打印", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                {
                    return;
                }

                // 禁用按钮，显示加载状态
                btnPrint.Enabled = false;
                btnPrint.Text = "打印中...";
                UpdateStatus("正在打印发运单...");

                // 逐个打印发运单
                int successCount = 0;
                foreach (var note in selectedNotes)
                {
                    try
                    {
                        // 获取发运单详细信息
                        var detailNote = await Task.Run(() => _soapService.GetShippingNoteDetail(note.ShippingNoteNumber));

                        // 打印发运单
                        await Task.Run(() => _printService.PrintShippingNoteDirect(detailNote));

                        successCount++;
                        UpdateStatus($"已打印 {successCount}/{selectedNotes.Count} 张发运单...");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"打印发运单 {note.ShippingNoteNumber} 时发生错误：{ex.Message}",
                            "打印错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }

                MessageBox.Show($"打印完成！成功打印 {successCount} 张发运单。", "打印完成",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // 打印完成后返回主界面
                ShowMainInterface();
                ResetForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印过程中发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnPrint.Enabled = true;
                btnPrint.Text = "打印发运单";
                UpdateStatus("");
            }
        }

        /// <summary>
        /// 获取选中的发运单
        /// </summary>
        private List<ShippingNote> GetSelectedShippingNotes()
        {
            var selectedNotes = new List<ShippingNote>();

            foreach (ListViewItem item in listViewShippingNotes.Items)
            {
                if (item.Checked && item.Tag is ShippingNote note)
                {
                    selectedNotes.Add(note);
                }
            }

            return selectedNotes;
        }

        /// <summary>
        /// 返回按钮点击事件（返回首页）
        /// </summary>
        private void btnBack_Click(object sender, EventArgs e)
        {
            ShowMainInterface();
            ResetForm();
        }

        /// <summary>
        /// 重置表单
        /// </summary>
        private void ResetForm()
        {
            txtPhoneNumber.Text = "";
            txtVerificationCode.Text = "";
            _currentPhoneNumber = "";
            _shippingNotes.Clear();
            listViewShippingNotes.Items.Clear();
            lblShippingNotesCount.Text = "共找到 0 张发运单";
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatus(string message)
        {
            lblStatus.Text = message;
            Application.DoEvents();
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // 调整布局以适应当前窗体大小
            AdjustLayout();
        }

        /// <summary>
        /// 窗体大小变化事件
        /// </summary>
        private void MainForm_Resize(object sender, EventArgs e)
        {
            // 调整布局以适应新的窗体大小
            AdjustLayout();
        }

        /// <summary>
        /// 调整布局以适应窗体大小
        /// </summary>
        private void AdjustLayout()
        {
            if (this.WindowState == FormWindowState.Minimized)
                return;

            // 调整标题栏
            AdjustTitleBar();

            // 调整卡片容器布局
            AdjustCardContainer();

            // 调整状态栏
            AdjustStatusBar();
        }

        /// <summary>
        /// 调整标题栏布局
        /// </summary>
        private void AdjustTitleBar()
        {
            if (lblTitle != null)
            {
                lblTitle.Size = new Size(this.ClientSize.Width, 80);
                lblTitle.Location = new Point(0, 0);
                lblTitle.TextAlign = ContentAlignment.MiddleCenter; // 居中显示
                lblTitle.Padding = new Padding(0); // 移除左边距
            }

            if (lblTime != null)
            {
                lblTime.Size = new Size(200, 80);
                lblTime.Location = new Point(this.ClientSize.Width - 200, 0);
                lblTime.TextAlign = ContentAlignment.MiddleCenter;
            }
        }

        /// <summary>
        /// 调整卡片容器布局
        /// </summary>
        private void AdjustCardContainer()
        {
            if (panelCardContainer != null)
            {
                // 计算卡片容器的位置和大小
                int margin = 40;
                int titleHeight = 80;
                int statusHeight = 50;

                int containerWidth = this.ClientSize.Width - (margin * 2);
                int containerHeight = this.ClientSize.Height - titleHeight - statusHeight - (margin * 2);

                panelCardContainer.Location = new Point(margin, titleHeight + margin);
                panelCardContainer.Size = new Size(containerWidth, containerHeight);

                // 调整卡片布局
                AdjustCardLayout();
            }
        }

        /// <summary>
        /// 调整卡片布局
        /// </summary>
        private void AdjustCardLayout()
        {
            if (panelCardContainer == null) return;

            // 卡片参数
            int cardWidth = 320;  // 增大卡片尺寸
            int cardHeight = 200;
            int cardSpacing = 40;

            // 检查是否只显示手机输入卡片
            bool onlyPhoneInput = cardPhoneInput != null && cardPhoneInput.Visible &&
                                 (cardVerifyCode == null || !cardVerifyCode.Visible) &&
                                 (cardSelectNotes == null || !cardSelectNotes.Visible) &&
                                 (cardPrintPreview == null || !cardPrintPreview.Visible) &&
                                 (cardPrintNotes == null || !cardPrintNotes.Visible) &&
                                 (cardHelp == null || !cardHelp.Visible);

            if (onlyPhoneInput)
            {
                // 单个卡片居中显示
                if (cardPhoneInput != null)
                {
                    int centerX = (panelCardContainer.Width - cardWidth) / 2;
                    int centerY = (panelCardContainer.Height - cardHeight) / 2;
                    cardPhoneInput.Location = new Point(centerX, centerY);
                    cardPhoneInput.Size = new Size(cardWidth, cardHeight);
                }
            }
            else
            {
                // 多个卡片的布局（如果需要的话）
                int cardsPerRow = Math.Max(1, (panelCardContainer.Width + cardSpacing) / (cardWidth + cardSpacing));

                if (cardsPerRow >= 3)
                {
                    int totalCardsWidth = 3 * cardWidth + 2 * cardSpacing;
                    int startX = (panelCardContainer.Width - totalCardsWidth) / 2;

                    // 第一行卡片
                    if (cardPhoneInput != null && cardPhoneInput.Visible)
                    {
                        cardPhoneInput.Location = new Point(startX, cardSpacing);
                        cardPhoneInput.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardVerifyCode != null && cardVerifyCode.Visible)
                    {
                        cardVerifyCode.Location = new Point(startX + cardWidth + cardSpacing, cardSpacing);
                        cardVerifyCode.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardSelectNotes != null && cardSelectNotes.Visible)
                    {
                        cardSelectNotes.Location = new Point(startX + 2 * (cardWidth + cardSpacing), cardSpacing);
                        cardSelectNotes.Size = new Size(cardWidth, cardHeight);
                    }

                    // 第二行卡片
                    int secondRowY = cardSpacing + cardHeight + cardSpacing;

                    if (cardPrintPreview != null && cardPrintPreview.Visible)
                    {
                        cardPrintPreview.Location = new Point(startX, secondRowY);
                        cardPrintPreview.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardPrintNotes != null && cardPrintNotes.Visible)
                    {
                        cardPrintNotes.Location = new Point(startX + cardWidth + cardSpacing, secondRowY);
                        cardPrintNotes.Size = new Size(cardWidth, cardHeight);
                    }

                    if (cardHelp != null && cardHelp.Visible)
                    {
                        cardHelp.Location = new Point(startX + 2 * (cardWidth + cardSpacing), secondRowY);
                        cardHelp.Size = new Size(cardWidth, cardHeight);
                    }
                }
            }
        }

        /// <summary>
        /// 调整步骤面板布局
        /// </summary>
        private void AdjustStepPanelLayout(Panel stepPanel)
        {
            if (stepPanel == null) return;

            // 计算面板的位置和大小
            int margin = 40;
            int titleHeight = 80;
            int statusHeight = 50;

            int panelWidth = this.ClientSize.Width - (margin * 2);
            int panelHeight = this.ClientSize.Height - titleHeight - statusHeight - (margin * 2);

            stepPanel.Location = new Point(margin, titleHeight + margin);
            stepPanel.Size = new Size(panelWidth, panelHeight);

            // 调整面板内控件的布局
            AdjustStepPanelControls(stepPanel);
        }

        /// <summary>
        /// 调整步骤面板内控件的布局
        /// </summary>
        private void AdjustStepPanelControls(Panel stepPanel)
        {
            if (stepPanel == null) return;

            // 为每个步骤面板添加返回首页按钮
            AddBackToHomeButton(stepPanel);

            // 根据面板类型调整控件布局
            if (stepPanel == panelStep1)
            {
                AdjustStep1Layout();
            }
            else if (stepPanel == panelStep2)
            {
                AdjustStep2Layout();
            }
            else if (stepPanel == panelStep3)
            {
                AdjustStep3Layout();
            }
        }

        /// <summary>
        /// 为步骤面板添加返回首页按钮
        /// </summary>
        private void AddBackToHomeButton(Panel stepPanel)
        {
            // 查找是否已存在返回首页按钮
            Button backToHomeBtn = stepPanel.Controls.OfType<Button>()
                .FirstOrDefault(b => b.Name == "btnBackToHome");

            if (backToHomeBtn == null)
            {
                // 创建返回首页按钮
                backToHomeBtn = new Button
                {
                    Name = "btnBackToHome",
                    Text = "⬅️ 返回首页",
                    Font = new Font("微软雅黑", 14F, FontStyle.Bold),
                    BackColor = Color.FromArgb(108, 117, 125),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Size = new Size(140, 50),
                    Location = new Point(20, stepPanel.Height - 70),
                    TabIndex = 100
                };
                backToHomeBtn.FlatAppearance.BorderSize = 0;
                backToHomeBtn.Click += (s, e) => ShowMainInterface();

                stepPanel.Controls.Add(backToHomeBtn);
            }
            else
            {
                // 更新位置
                backToHomeBtn.Location = new Point(20, stepPanel.Height - 70);
            }
        }

        /// <summary>
        /// 调整步骤1布局（手机号输入）
        /// </summary>
        private void AdjustStep1Layout()
        {
            if (panelStep1 == null) return;

            int centerX = panelStep1.Width / 2;
            int centerY = panelStep1.Height / 2;

            // 调整标题位置
            if (lblStep1 != null)
            {
                lblStep1.Location = new Point(20, 20);
                lblStep1.Size = new Size(panelStep1.Width - 40, 40);
                lblStep1.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整提示标签位置
            if (lblPhoneNumber != null)
            {
                lblPhoneNumber.Location = new Point(centerX - 170, centerY - 80);
                lblPhoneNumber.Size = new Size(340, 30);
                lblPhoneNumber.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整输入框位置
            if (txtPhoneNumber != null)
            {
                txtPhoneNumber.Location = new Point(centerX - 170, centerY - 25);
                txtPhoneNumber.Size = new Size(340, 50);
            }

            // 调整按钮位置
            if (btnSendCode != null)
            {
                btnSendCode.Location = new Point(centerX - 120, centerY + 50);
                btnSendCode.Size = new Size(240, 80);
            }
        }

        /// <summary>
        /// 调整步骤2布局（验证码输入）
        /// </summary>
        private void AdjustStep2Layout()
        {
            if (panelStep2 == null) return;

            int centerX = panelStep2.Width / 2;
            int centerY = panelStep2.Height / 2;

            // 调整标题位置
            if (lblStep2 != null)
            {
                lblStep2.Location = new Point(20, 20);
                lblStep2.Size = new Size(panelStep2.Width - 40, 40);
                lblStep2.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整提示标签位置
            if (lblVerificationCode != null)
            {
                lblVerificationCode.Location = new Point(centerX - 170, centerY - 80);
                lblVerificationCode.Size = new Size(340, 30);
                lblVerificationCode.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整输入框位置
            if (txtVerificationCode != null)
            {
                txtVerificationCode.Location = new Point(centerX - 170, centerY - 25);
                txtVerificationCode.Size = new Size(340, 50);
            }

            // 调整按钮位置
            if (btnVerifyCode != null)
            {
                btnVerifyCode.Location = new Point(centerX - 120, centerY + 50);
                btnVerifyCode.Size = new Size(240, 80);
            }
        }

        /// <summary>
        /// 调整步骤3布局（发运单列表）
        /// </summary>
        private void AdjustStep3Layout()
        {
            if (panelStep3 == null) return;

            // 调整标题位置
            if (lblStep3 != null)
            {
                lblStep3.Location = new Point(20, 20);
                lblStep3.Size = new Size(panelStep3.Width - 40, 40);
                lblStep3.TextAlign = ContentAlignment.MiddleCenter;
            }

            // 调整发运单数量标签位置
            if (lblShippingNotesCount != null)
            {
                lblShippingNotesCount.Location = new Point(20, 70);
                lblShippingNotesCount.Size = new Size(panelStep3.Width - 40, 30);
                lblShippingNotesCount.TextAlign = ContentAlignment.MiddleLeft;
            }

            // 调整列表视图位置
            if (listViewShippingNotes != null)
            {
                listViewShippingNotes.Location = new Point(20, 110);
                listViewShippingNotes.Size = new Size(panelStep3.Width - 40, panelStep3.Height - 220);
            }

            // 调整按钮位置
            int buttonY = panelStep3.Height - 100;
            int buttonSpacing = 20;
            int buttonWidth = 140;
            int buttonHeight = 60;

            if (btnSelectAll != null)
            {
                btnSelectAll.Location = new Point(20, buttonY);
                btnSelectAll.Size = new Size(buttonWidth, buttonHeight);
            }

            if (btnSelectNone != null)
            {
                btnSelectNone.Location = new Point(20 + buttonWidth + buttonSpacing, buttonY);
                btnSelectNone.Size = new Size(buttonWidth, buttonHeight);
            }

            if (btnPreview != null)
            {
                btnPreview.Location = new Point(panelStep3.Width - 2 * (buttonWidth + buttonSpacing) - 20, buttonY);
                btnPreview.Size = new Size(buttonWidth + 40, buttonHeight);
            }

            if (btnPrint != null)
            {
                btnPrint.Location = new Point(panelStep3.Width - buttonWidth - 20, buttonY);
                btnPrint.Size = new Size(buttonWidth + 40, buttonHeight);
            }
        }

        /// <summary>
        /// 调整状态栏布局
        /// </summary>
        private void AdjustStatusBar()
        {
            if (lblStatus != null)
            {
                lblStatus.Location = new Point(40, this.ClientSize.Height - 50);
                lblStatus.Size = new Size(this.ClientSize.Width - 80, 30);
                lblStatus.TextAlign = ContentAlignment.MiddleCenter;
            }
        }

        /// <summary>
        /// 应用现代化样式
        /// </summary>
        private void ApplyModernStyles()
        {
            try
            {
                // 应用标题栏波浪背景
                ModernUI.AddWaveBackground(lblTitle);

                // 应用卡片样式
                if (cardPhoneInput != null)
                    ModernUI.ApplyCardStyle(cardPhoneInput, ModernUI.Colors.Primary);

                if (cardVerifyCode != null)
                    ModernUI.ApplyCardStyle(cardVerifyCode, ModernUI.Colors.Secondary);

                if (cardSelectNotes != null)
                    ModernUI.ApplyCardStyle(cardSelectNotes, ModernUI.Colors.Purple);

                if (cardPrintPreview != null)
                    ModernUI.ApplyCardStyle(cardPrintPreview, ModernUI.Colors.Warning);

                if (cardPrintNotes != null)
                    ModernUI.ApplyCardStyle(cardPrintNotes, ModernUI.Colors.Secondary);

                if (cardHelp != null)
                    ModernUI.ApplyCardStyle(cardHelp, ModernUI.Colors.Info);

                // 应用按钮样式
                ApplyButtonStyles();

                // 应用文本框样式
                ApplyTextBoxStyles();
            }
            catch (Exception ex)
            {
                // 如果样式应用失败，记录错误但不影响程序运行
                System.Diagnostics.Debug.WriteLine($"应用现代化样式时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用按钮样式
        /// </summary>
        private void ApplyButtonStyles()
        {
            var buttons = new[]
            {
                btnSendCode, btnVerifyCode, btnPrint, btnPreview, btnBack,
                btnSelectAll, btnSelectNone, btnConfirmSelection,
                btnBackToMain3, btnPhoneInput, btnVerifyCode, btnSelectNotes,
                btnPrintPreview, btnPrintNotes, btnHelp
            };

            foreach (var button in buttons)
            {
                if (button != null)
                {
                    ModernUI.ApplyButtonStyle(button, button.BackColor);
                }
            }
        }

        /// <summary>
        /// 应用文本框样式
        /// </summary>
        private void ApplyTextBoxStyles()
        {
            var textBoxes = new[] { txtPhoneNumber, txtVerificationCode };

            foreach (var textBox in textBoxes)
            {
                if (textBox != null)
                {
                    ModernUI.ApplyTextBoxStyle(textBox);
                }
            }
        }

        /// <summary>
        /// 窗体关闭时释放资源
        /// </summary>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _printService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
