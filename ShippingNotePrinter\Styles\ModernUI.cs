using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace ShippingNotePrinter.Styles
{
    /// <summary>
    /// 现代化UI样式类，提供圆角、阴影等效果
    /// </summary>
    public static class ModernUI
    {
        /// <summary>
        /// 为控件添加圆角效果
        /// </summary>
        /// <param name="control">要添加圆角的控件</param>
        /// <param name="radius">圆角半径</param>
        public static void AddRoundedCorners(Control control, int radius = 15)
        {
            GraphicsPath path = new GraphicsPath();
            Rectangle rect = new Rectangle(0, 0, control.Width, control.Height);
            
            // 创建圆角矩形路径
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();
            
            control.Region = new Region(path);
        }

        /// <summary>
        /// 为面板添加阴影效果
        /// </summary>
        /// <param name="panel">要添加阴影的面板</param>
        public static void AddShadowEffect(Panel panel)
        {
            panel.Paint += (sender, e) =>
            {
                // 绘制阴影
                Rectangle shadowRect = new Rectangle(5, 5, panel.Width - 5, panel.Height - 5);
                using (SolidBrush shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, shadowRect);
                }
            };
        }

        /// <summary>
        /// 创建渐变背景
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="startColor">起始颜色</param>
        /// <param name="endColor">结束颜色</param>
        public static void AddGradientBackground(Control control, Color startColor, Color endColor)
        {
            control.Paint += (sender, e) =>
            {
                Rectangle rect = control.ClientRectangle;
                using (LinearGradientBrush brush = new LinearGradientBrush(rect, startColor, endColor, LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, rect);
                }
            };
        }

        /// <summary>
        /// 为标题栏添加波浪纹理背景
        /// </summary>
        /// <param name="control">控件</param>
        public static void AddWaveBackground(Control control)
        {
            control.Paint += (sender, e) =>
            {
                Graphics g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;
                
                // 主背景渐变
                Rectangle rect = control.ClientRectangle;
                using (LinearGradientBrush mainBrush = new LinearGradientBrush(
                    rect, 
                    Color.FromArgb(64, 123, 255), 
                    Color.FromArgb(100, 149, 237), 
                    LinearGradientMode.Horizontal))
                {
                    g.FillRectangle(mainBrush, rect);
                }
                
                // 添加波浪纹理
                using (Pen wavePen = new Pen(Color.FromArgb(30, 255, 255, 255), 2))
                {
                    for (int i = 0; i < control.Width + 50; i += 50)
                    {
                        GraphicsPath wavePath = new GraphicsPath();
                        Point[] wavePoints = new Point[]
                        {
                            new Point(i - 25, control.Height),
                            new Point(i, control.Height - 20),
                            new Point(i + 25, control.Height - 10),
                            new Point(i + 50, control.Height)
                        };
                        wavePath.AddCurve(wavePoints);
                        g.DrawPath(wavePen, wavePath);
                    }
                }
            };
        }

        /// <summary>
        /// 应用现代化卡片样式
        /// </summary>
        /// <param name="card">卡片面板</param>
        /// <param name="backgroundColor">背景颜色</param>
        public static void ApplyCardStyle(Panel card, Color backgroundColor)
        {
            card.BackColor = backgroundColor;
            AddRoundedCorners(card, 20);
            
            // 添加悬停效果
            card.MouseEnter += (sender, e) =>
            {
                card.BackColor = ControlPaint.Light(backgroundColor, 0.1f);
            };
            
            card.MouseLeave += (sender, e) =>
            {
                card.BackColor = backgroundColor;
            };
        }

        /// <summary>
        /// 应用现代化按钮样式
        /// </summary>
        /// <param name="button">按钮</param>
        /// <param name="backgroundColor">背景颜色</param>
        public static void ApplyButtonStyle(Button button, Color backgroundColor)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.BackColor = backgroundColor;
            button.ForeColor = Color.White;
            AddRoundedCorners(button, 10);
            
            // 添加悬停效果
            button.MouseEnter += (sender, e) =>
            {
                button.BackColor = ControlPaint.Light(backgroundColor, 0.2f);
            };
            
            button.MouseLeave += (sender, e) =>
            {
                button.BackColor = backgroundColor;
            };
        }

        /// <summary>
        /// 应用现代化文本框样式
        /// </summary>
        /// <param name="textBox">文本框</param>
        public static void ApplyTextBoxStyle(TextBox textBox)
        {
            textBox.BorderStyle = BorderStyle.None;
            textBox.BackColor = Color.White;
            
            // 创建包装面板来实现圆角边框
            Panel wrapper = new Panel();
            wrapper.Size = new Size(textBox.Width + 20, textBox.Height + 20);
            wrapper.Location = textBox.Location;
            wrapper.BackColor = Color.FromArgb(230, 230, 230);
            
            textBox.Location = new Point(10, 10);
            textBox.Parent.Controls.Remove(textBox);
            wrapper.Controls.Add(textBox);
            textBox.Parent.Controls.Add(wrapper);
            
            AddRoundedCorners(wrapper, 10);
        }

        /// <summary>
        /// 获取现代化颜色方案
        /// </summary>
        public static class Colors
        {
            public static readonly Color Primary = Color.FromArgb(64, 123, 255);
            public static readonly Color Secondary = Color.FromArgb(255, 107, 107);
            public static readonly Color Success = Color.FromArgb(40, 167, 69);
            public static readonly Color Warning = Color.FromArgb(255, 193, 7);
            public static readonly Color Info = Color.FromArgb(23, 162, 184);
            public static readonly Color Purple = Color.FromArgb(124, 77, 255);
            public static readonly Color Background = Color.FromArgb(248, 250, 252);
            public static readonly Color CardBackground = Color.White;
            public static readonly Color TextPrimary = Color.FromArgb(33, 37, 41);
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125);
        }
    }
}
